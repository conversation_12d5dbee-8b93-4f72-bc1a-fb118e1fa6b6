<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>酒店翻译映射系统测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .test-section h2 {
            color: #34495e;
            margin-top: 0;
        }
        .test-case {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }
        .test-input {
            flex: 1;
            font-weight: bold;
            color: #e74c3c;
        }
        .test-arrow {
            margin: 0 15px;
            color: #7f8c8d;
        }
        .test-output {
            flex: 1;
            font-weight: bold;
            color: #27ae60;
        }
        .test-status {
            margin-left: 15px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pass {
            background-color: #d4edda;
            color: #155724;
        }
        .status-fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .console-output {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏨 酒店翻译映射系统测试</h1>
        
        <div class="test-section">
            <h2>🔧 系统状态检查</h2>
            <button class="btn" onclick="checkSystemStatus()">检查系统状态</button>
            <button class="btn" onclick="runAllTests()">运行所有测试</button>
            <button class="btn" onclick="clearConsole()">清空控制台</button>
        </div>

        <div class="test-section">
            <h2>🚨 关键问题修复测试</h2>
            <div id="critical-tests"></div>
        </div>

        <div class="test-section">
            <h2>🏢 国际品牌标准化测试</h2>
            <div id="brand-tests"></div>
        </div>

        <div class="test-section">
            <h2>🌍 地区酒店测试</h2>
            <div id="regional-tests"></div>
        </div>

        <div class="test-section">
            <h2>🎨 文化主题酒店测试</h2>
            <div id="cultural-tests"></div>
        </div>

        <div class="test-section">
            <h2>🔍 模糊匹配测试</h2>
            <div id="fuzzy-tests"></div>
        </div>

        <div class="test-section">
            <h2>✅ 翻译验证测试</h2>
            <div id="validation-tests"></div>
        </div>

        <div class="test-section">
            <h2>⚡ 性能测试</h2>
            <button class="btn" onclick="runPerformanceTests()">运行性能测试</button>
            <div id="performance-results"></div>
        </div>

        <div class="test-section">
            <h2>📊 测试统计</h2>
            <div class="stats" id="test-stats"></div>
        </div>

        <div class="test-section">
            <h2>🖥️ 控制台输出</h2>
            <div class="console-output" id="console-output"></div>
        </div>
    </div>

    <!-- 引入酒店映射系统 -->
    <script src="UNIFIED_HOTEL_MAPPINGS.js"></script>
    
    <script>
        // 扩展的测试用例定义
        const testCases = {
            critical: [
                { input: '莱恩酒店', expected: 'Sleeping Lion Hotel', description: '核心问题：莱恩酒店翻译' },
                { input: '莱恩套房酒店', expected: 'Sleeping Lion Suites', description: '莱恩套房酒店翻译' },
                { input: '滨海湾金沙', expected: 'Marina Bay Sands', description: '新加坡地标：滨海湾金沙' },
                { input: '东方大酒店', expected: 'Eastern & Oriental Hotel', description: '槟城文化遗产：东方大酒店' },
                { input: '富丽敦酒店', expected: 'The Fullerton Hotel Singapore', description: '新加坡富丽敦酒店' },
                { input: '金沙酒店', expected: 'Marina Bay Sands', description: '金沙酒店别名测试' },
                { input: '蓝屋酒店', expected: 'Cheong Fatt Tze Mansion', description: '槟城蓝屋历史建筑' }
            ],
            brands: [
                { input: '万豪酒店', expected: 'Marriott Hotel', description: '万豪品牌标准化' },
                { input: '希尔顿酒店', expected: 'Hilton Hotel', description: '希尔顿品牌标准化' },
                { input: '香格里拉酒店', expected: 'Shangri-La Hotel', description: '香格里拉品牌标准化' },
                { input: '丽思卡尔顿', expected: 'The Ritz-Carlton', description: '丽思卡尔顿品牌标准化' },
                { input: '凯悦酒店', expected: 'Hyatt Hotel', description: '凯悦品牌标准化' },
                { input: '洲际酒店', expected: 'InterContinental Hotel', description: '洲际品牌标准化' },
                { input: '康拉德酒店', expected: 'Conrad Hotel', description: '康拉德品牌标准化' },
                { input: '四季酒店', expected: 'Four Seasons Hotel', description: '四季品牌标准化' }
            ],
            regional: [
                { input: '吉隆坡香格里拉酒店', expected: 'Shangri-La Hotel Kuala Lumpur', description: '吉隆坡地区酒店' },
                { input: '新加坡史丹福瑞士', expected: 'Swissôtel The Stamford', description: '新加坡地区酒店' },
                { input: '槟城香格里拉度假村', expected: 'Shangri-La\'s Rasa Sayang Resort & Spa', description: '槟城地区酒店' },
                { input: '亚庇凯悦丽晶', expected: 'Hyatt Regency Kinabalu', description: '亚庇地区酒店' },
                { input: '龙门客栈', expected: 'Dragon Inn Floating Resort', description: '仙本那地区酒店' },
                { input: '新山逸林希尔顿', expected: 'DoubleTree by Hilton Hotel Johor Bahru', description: '新山地区酒店' }
            ],
            cultural: [
                { input: '皇家酒店', expected: 'Royal Hotel', description: '皇室主题酒店' },
                { input: '钻石酒店', expected: 'Diamond Hotel', description: '宝石主题酒店' },
                { input: '龙酒店', expected: 'Dragon Hotel', description: '神话主题酒店' },
                { input: '花园酒店', expected: 'Garden Hotel', description: '自然主题酒店' },
                { input: '玫瑰酒店', expected: 'Rose Hotel', description: '花卉主题酒店' }
            ],
            fuzzy: [
                { input: '吉隆坡万豪', expected: 'JW Marriott Hotel Kuala Lumpur', description: '模糊匹配：品牌+地区' },
                { input: '新加坡希尔顿', expected: 'Hilton Singapore', description: '模糊匹配：品牌+地区' },
                { input: '槟城硬石', expected: 'Hard Rock Hotel Penang', description: '模糊匹配：品牌简称' },
                { input: '亚庇香格里拉', expected: 'Shangri-La\'s Tanjung Aru Resort & Spa', description: '模糊匹配：复杂度假村' }
            ],
            validation: [
                { input: '莱恩酒店', badTranslation: 'Lane Hotel', description: '验证：检测错误字面翻译' },
                { input: '万豪酒店', badTranslation: 'Wanhao Hotel', description: '验证：检测拼音翻译' },
                { input: '希尔顿酒店', badTranslation: 'Hill Ton Hotel', description: '验证：检测字面翻译' },
                { input: '滨海湾金沙', badTranslation: 'Marina Bay Gold Sand', description: '验证：检测错误地标翻译' }
            ]
        };

        let testResults = {
            total: 0,
            passed: 0,
            failed: 0
        };

        // 控制台输出重定向
        const originalConsoleLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.join(' ');
            consoleOutput.innerHTML += message + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        // 检查系统状态
        function checkSystemStatus() {
            console.log('🔍 检查酒店翻译映射系统状态...');
            
            if (typeof UNIFIED_HOTEL_MAPPINGS !== 'undefined') {
                console.log('✅ UNIFIED_HOTEL_MAPPINGS 已加载');
                console.log(`📊 总映射数量: ${Object.keys(UNIFIED_HOTEL_MAPPINGS).length}`);
            } else {
                console.log('❌ UNIFIED_HOTEL_MAPPINGS 未找到');
            }
            
            if (typeof translateHotelName === 'function') {
                console.log('✅ translateHotelName 函数可用');
            } else {
                console.log('❌ translateHotelName 函数未找到');
            }
            
            if (typeof integrateUnifiedHotelMappings === 'function') {
                console.log('✅ integrateUnifiedHotelMappings 函数可用');
                // 初始化系统
                integrateUnifiedHotelMappings();
            } else {
                console.log('❌ integrateUnifiedHotelMappings 函数未找到');
            }
        }

        // 运行单个测试
        function runTest(testCase) {
            testResults.total++;
            
            if (typeof translateHotelName === 'function') {
                const result = translateHotelName(testCase.input);
                const passed = result === testCase.expected;
                
                if (passed) {
                    testResults.passed++;
                } else {
                    testResults.failed++;
                    console.log(`❌ 测试失败: ${testCase.input} → 期望: ${testCase.expected}, 实际: ${result}`);
                }
                
                return { ...testCase, result, passed };
            } else {
                testResults.failed++;
                console.log(`❌ 翻译函数不可用: ${testCase.input}`);
                return { ...testCase, result: '函数不可用', passed: false };
            }
        }

        // 渲染测试结果
        function renderTestResults(containerId, tests) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            tests.forEach(test => {
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                
                const statusClass = test.passed ? 'status-pass' : 'status-fail';
                const statusText = test.passed ? '✅ 通过' : '❌ 失败';
                
                testDiv.innerHTML = `
                    <div class="test-input">${test.input}</div>
                    <div class="test-arrow">→</div>
                    <div class="test-output">${test.result}</div>
                    <div class="test-status ${statusClass}">${statusText}</div>
                `;
                
                container.appendChild(testDiv);
            });
        }

        // 更新统计信息
        function updateStats() {
            const statsContainer = document.getElementById('test-stats');
            const passRate = testResults.total > 0 ? (testResults.passed / testResults.total * 100).toFixed(1) : 0;
            
            statsContainer.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${testResults.total}</div>
                    <div class="stat-label">总测试数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${testResults.passed}</div>
                    <div class="stat-label">通过测试</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${testResults.failed}</div>
                    <div class="stat-label">失败测试</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${passRate}%</div>
                    <div class="stat-label">通过率</div>
                </div>
            `;
        }

        // 运行验证测试
        function runValidationTest(testCase) {
            testResults.total++;

            if (typeof validateHotelTranslation === 'function') {
                const validation = validateHotelTranslation(testCase.input, testCase.badTranslation);
                const passed = !validation.isValid; // 应该检测出问题翻译

                if (passed) {
                    testResults.passed++;
                } else {
                    testResults.failed++;
                    console.log(`❌ 验证测试失败: ${testCase.input} - 未能检测出问题翻译: ${testCase.badTranslation}`);
                }

                return {
                    ...testCase,
                    result: passed ? '✅ 检测到问题' : '❌ 未检测到问题',
                    passed,
                    validation: validation
                };
            } else {
                testResults.failed++;
                console.log(`❌ 验证函数不可用: ${testCase.input}`);
                return { ...testCase, result: '验证函数不可用', passed: false };
            }
        }

        // 运行性能测试
        function runPerformanceTests() {
            console.log('⚡ 开始性能测试...');

            const performanceResults = document.getElementById('performance-results');
            performanceResults.innerHTML = '<p>正在运行性能测试...</p>';

            // 测试数据集
            const testData = [
                '莱恩酒店', '万豪酒店', '希尔顿酒店', '香格里拉酒店', '滨海湾金沙',
                '吉隆坡香格里拉酒店', '新加坡史丹福瑞士', '槟城香格里拉度假村',
                '皇家酒店', '钻石酒店', '龙酒店', '花园酒店', '玫瑰酒店'
            ];

            // 测试翻译性能
            const startTime = performance.now();
            let translationCount = 0;

            for (let i = 0; i < 100; i++) {
                testData.forEach(hotelName => {
                    if (typeof translateHotelName === 'function') {
                        translateHotelName(hotelName);
                        translationCount++;
                    }
                });
            }

            const endTime = performance.now();
            const totalTime = endTime - startTime;
            const avgTime = totalTime / translationCount;

            // 测试模糊匹配性能
            const fuzzyStartTime = performance.now();
            let fuzzyCount = 0;

            if (typeof performIntelligentFuzzyMatch === 'function') {
                for (let i = 0; i < 50; i++) {
                    testData.forEach(hotelName => {
                        performIntelligentFuzzyMatch(hotelName);
                        fuzzyCount++;
                    });
                }
            }

            const fuzzyEndTime = performance.now();
            const fuzzyTotalTime = fuzzyEndTime - fuzzyStartTime;
            const fuzzyAvgTime = fuzzyCount > 0 ? fuzzyTotalTime / fuzzyCount : 0;

            // 显示结果
            performanceResults.innerHTML = `
                <div style="background: white; padding: 15px; border-radius: 6px; margin-top: 10px;">
                    <h4>性能测试结果</h4>
                    <p><strong>基础翻译性能:</strong></p>
                    <ul>
                        <li>总翻译次数: ${translationCount}</li>
                        <li>总耗时: ${totalTime.toFixed(2)}ms</li>
                        <li>平均耗时: ${avgTime.toFixed(4)}ms/次</li>
                        <li>每秒处理: ${(1000/avgTime).toFixed(0)} 次</li>
                    </ul>
                    <p><strong>模糊匹配性能:</strong></p>
                    <ul>
                        <li>总匹配次数: ${fuzzyCount}</li>
                        <li>总耗时: ${fuzzyTotalTime.toFixed(2)}ms</li>
                        <li>平均耗时: ${fuzzyAvgTime.toFixed(4)}ms/次</li>
                        <li>每秒处理: ${fuzzyCount > 0 ? (1000/fuzzyAvgTime).toFixed(0) : 'N/A'} 次</li>
                    </ul>
                </div>
            `;

            console.log(`⚡ 性能测试完成 - 基础翻译: ${avgTime.toFixed(4)}ms/次, 模糊匹配: ${fuzzyAvgTime.toFixed(4)}ms/次`);
        }

        // 运行所有测试
        function runAllTests() {
            console.log('🧪 开始运行所有测试...');

            // 重置统计
            testResults = { total: 0, passed: 0, failed: 0 };

            // 运行关键测试
            console.log('🚨 运行关键问题修复测试...');
            const criticalResults = testCases.critical.map(runTest);
            renderTestResults('critical-tests', criticalResults);

            // 运行品牌测试
            console.log('🏢 运行国际品牌标准化测试...');
            const brandResults = testCases.brands.map(runTest);
            renderTestResults('brand-tests', brandResults);

            // 运行地区测试
            console.log('🌍 运行地区酒店测试...');
            const regionalResults = testCases.regional.map(runTest);
            renderTestResults('regional-tests', regionalResults);

            // 运行文化主题测试
            console.log('🎨 运行文化主题酒店测试...');
            const culturalResults = testCases.cultural.map(runTest);
            renderTestResults('cultural-tests', culturalResults);

            // 运行模糊匹配测试
            console.log('🔍 运行模糊匹配测试...');
            const fuzzyResults = testCases.fuzzy.map(runTest);
            renderTestResults('fuzzy-tests', fuzzyResults);

            // 运行验证测试
            console.log('✅ 运行翻译验证测试...');
            const validationResults = testCases.validation.map(runValidationTest);
            renderTestResults('validation-tests', validationResults);

            // 更新统计
            updateStats();

            console.log('✅ 所有测试完成');
            console.log(`📊 测试结果: ${testResults.passed}/${testResults.total} 通过 (${(testResults.passed/testResults.total*100).toFixed(1)}%)`);
        }

        // 清空控制台
        function clearConsole() {
            consoleOutput.innerHTML = '';
        }

        // 页面加载时自动检查系统状态
        window.addEventListener('load', function() {
            setTimeout(checkSystemStatus, 500);
        });
    </script>
</body>
</html>
