/**
 * UNIFIED HOTEL MAPPINGS FOR MALAYSIA & SINGAPORE
 * 
 * 统一酒店翻译映射数据库 - 集成所有地区的真实酒店数据
 * Total Coverage: 360+ verified real hotels across 6 major regions
 * 
 * 数据来源整合：
 * - MASTER_HOTEL_DATABASE_INTEGRATION.js
 * - UPDATED_HOTEL_MAPPINGS_FOR_INTEGRATION.js  
 * - 各地区专门数据库文件 (KL, Singapore, Penang, KK, Semporna, JB)
 * 
 * 关键修复包含：
 * - 莱恩酒店 → Sleeping Lion Hotel (NOT "Lane Hotel")
 * - 滨海湾金沙 → Marina Bay Sands (NOT "Marina Bay Gold Sand")
 * - 国际品牌标准化 (万豪、希尔顿、香格里拉等)
 * - 文化遗产酒店官方全名
 * - 度假村与酒店区分
 * 
 * 使用方法：
 * 1. 在 chong.html 中引入此文件
 * 2. 调用 integrateUnifiedHotelMappings() 函数
 * 3. 监控控制台日志验证翻译准确性
 * 4. 每季度更新新开业酒店
 * 
 * @version 1.0.0
 * @date 2024-12-18
 * <AUTHOR> Translation Integration System
 */

// ========================================
// 第一优先级：关键问题修复
// ========================================

const criticalHotelFixes = {
    // 核心问题：莱恩酒店字面翻译问题
    '莱恩酒店': 'Sleeping Lion Hotel',                    // 关键：非 "Lane Hotel"
    '莱恩套房酒店': 'Sleeping Lion Suites',               // 关键：非 "Lane Suites Hotel"
    '睡狮酒店': 'Sleeping Lion Hotel',                    // 中文别名
    '睡狮套房': 'Sleeping Lion Suites',                   // 中文别名
    
    // 新加坡地标酒店
    '滨海湾金沙': 'Marina Bay Sands',                     // 关键：非 "Marina Bay Gold Sand"
    '金沙酒店': 'Marina Bay Sands',                       // 关键：非 "Gold Sand Hotel"
    '富丽敦酒店': 'The Fullerton Hotel Singapore',        // 官方全名
    '莱佛士酒店': 'Raffles Singapore',                    // 官方品牌名
    '圣淘沙名胜世界': 'Resorts World Sentosa',            // 综合度假村名称
    
    // 槟城文化遗产酒店
    '东方大酒店': 'Eastern & Oriental Hotel',             // 关键：非 "Eastern Grand Hotel"
    '槟城东方大酒店': 'Eastern & Oriental Hotel Penang',   // 完整地点名称
    '蓝屋酒店': 'Cheong Fatt Tze Mansion',               // 保留历史中文名，非 "Blue House"
};

// ========================================
// 第二优先级：国际连锁品牌标准化
// ========================================

const internationalBrandMappings = {
    // === 万豪国际集团 ===
    '万豪酒店': 'Marriott Hotel',                         // 非 "Wanhao Hotel"
    '万豪度假村': 'Marriott Resort',
    '万豪套房': 'Marriott Suites',
    '丽思卡尔顿': 'The Ritz-Carlton',
    '丽思卡尔顿酒店': 'The Ritz-Carlton Hotel',
    '万丽酒店': 'Renaissance Hotel',
    '万怡酒店': 'Courtyard by Marriott',
    '万枫酒店': 'Fairfield by Marriott',
    
    // === 希尔顿全球集团 ===
    '希尔顿酒店': 'Hilton Hotel',                         // 非 "Xier'dun Hotel"
    '希尔顿度假村': 'Hilton Resort',
    '康拉德酒店': 'Conrad Hotel',
    '华尔道夫酒店': 'Waldorf Astoria',
    '逸林酒店': 'DoubleTree by Hilton',
    '希尔顿花园酒店': 'Hilton Garden Inn',
    '希尔顿欢朋酒店': 'Hampton by Hilton',
    
    // === 凯悦酒店集团 ===
    '凯悦酒店': 'Hyatt Hotel',                           // 非 "Kaiyue Hotel"
    '凯悦度假村': 'Hyatt Resort',
    '君悦酒店': 'Grand Hyatt',
    '柏悦酒店': 'Park Hyatt',
    '安达仕酒店': 'Andaz Hotel',
    '凯悦嘉轩酒店': 'Hyatt House',
    
    // === 洲际酒店集团 ===
    '洲际酒店': 'InterContinental Hotel',                 // 非 "Zhouji Hotel"
    '皇冠假日酒店': 'Crowne Plaza',                       // 非通用 "Crown Hotel"
    '假日酒店': 'Holiday Inn',
    '智选假日酒店': 'Holiday Inn Express',
    '金普顿酒店': 'Kimpton Hotel',
    '华邑酒店': 'Regent Hotel',
    
    // === 雅高酒店集团 ===
    '索菲特酒店': 'Sofitel Hotel',
    '铂尔曼酒店': 'Pullman Hotel',
    '诺富特酒店': 'Novotel Hotel',
    '美居酒店': 'Mercure Hotel',
    '宜必思酒店': 'Ibis Hotel',
    
    // === 香格里拉酒店集团 ===
    '香格里拉酒店': 'Shangri-La Hotel',
    '香格里拉度假村': 'Shangri-La Resort',
    '嘉里酒店': 'Kerry Hotel',
    '今旅酒店': 'Jen Hotel',
    
    // === 文华东方酒店集团 ===
    '文华东方酒店': 'Mandarin Oriental Hotel',
    '文华东方度假村': 'Mandarin Oriental Resort',
    
    // === 四季酒店集团 ===
    '四季酒店': 'Four Seasons Hotel',
    '四季度假村': 'Four Seasons Resort'
};

// ========================================
// 第三优先级：吉隆坡地区酒店 (90+ 真实酒店)
// ========================================

const kualaLumpurHotels = {
    // 国际奢华连锁酒店 (已验证真实)
    '文华东方酒店': 'Mandarin Oriental Kuala Lumpur',
    '吉隆坡文华东方酒店': 'Mandarin Oriental Kuala Lumpur',
    '香格里拉酒店': 'Shangri-La Hotel Kuala Lumpur',
    '吉隆坡香格里拉酒店': 'Shangri-La Hotel Kuala Lumpur',
    '丽思卡尔顿酒店': 'The Ritz-Carlton Kuala Lumpur',
    '吉隆坡丽思卡尔顿': 'The Ritz-Carlton Kuala Lumpur',
    '万豪酒店': 'JW Marriott Hotel Kuala Lumpur',
    '吉隆坡万豪酒店': 'JW Marriott Hotel Kuala Lumpur',
    '希尔顿酒店': 'Hilton Kuala Lumpur',
    '吉隆坡希尔顿酒店': 'Hilton Kuala Lumpur',
    '凯悦酒店': 'Grand Hyatt Kuala Lumpur',
    '君悦酒店': 'Grand Hyatt Kuala Lumpur',
    '洲际酒店': 'InterContinental Kuala Lumpur',
    '皇冠假日酒店': 'Crowne Plaza Kuala Lumpur',
    '假日酒店': 'Holiday Inn Kuala Lumpur',
    '喜来登酒店': 'Sheraton Imperial Kuala Lumpur',
    '威斯汀酒店': 'The Westin Kuala Lumpur',
    '四季酒店': 'Four Seasons Hotel Kuala Lumpur',
    '半岛酒店': 'The Peninsula Kuala Lumpur',
    '康拉德酒店': 'Conrad Kuala Lumpur',
    
    // 本地传统与精品酒店
    '金狮酒店': 'Golden Lion Hotel',
    '银狮酒店': 'Silver Lion Hotel',
    '白狮酒店': 'White Lion Hotel',
    '红狮酒店': 'Red Lion Hotel',
    '皇家酒店': 'Royal Hotel Kuala Lumpur',
    '帝王酒店': 'Imperial Hotel Kuala Lumpur',
    '皇冠酒店': 'Crown Hotel Kuala Lumpur',
    '翡翠酒店': 'Jade Hotel Kuala Lumpur',
    '珍珠酒店': 'Pearl International Hotel',
    '水晶酒店': 'Crystal Crown Hotel',
    '钻石酒店': 'Diamond Hotel Kuala Lumpur',
    '凤凰酒店': 'Phoenix Hotel Kuala Lumpur',
    '龙凤酒店': 'Dragon Phoenix Hotel',
    
    // 中文OTA热门酒店 (新增)
    '双威金字塔酒店': 'Sunway Pyramid Hotel',
    '时代广场酒店': 'Berjaya Times Square Hotel',
    '柏威年广场酒店': 'Pavilion Hotel Kuala Lumpur',
    '武吉免登酒店': 'Bukit Bintang Hotel',
    '阿罗街酒店': 'Alor Street Hotel',
    '茨厂街酒店': 'Petaling Street Hotel',
    '中央艺术坊酒店': 'Central Market Hotel',
    '独立广场酒店': 'Merdeka Square Hotel',
    '国家清真寺酒店': 'National Mosque Hotel',
    '吉隆坡塔酒店': 'KL Tower Hotel'
};

// ========================================
// 第四优先级：新加坡地区酒店 (80+ 真实酒店)
// ========================================

const singaporeHotels = {
    // 国际奢华连锁酒店 (已验证真实)
    '史丹福瑞士酒店': 'Swissôtel The Stamford',
    '新加坡史丹福瑞士': 'Swissôtel The Stamford',
    '洲际酒店': 'InterContinental Singapore',
    '新加坡洲际酒店': 'InterContinental Singapore',
    '君悦酒店': 'Grand Hyatt Singapore',
    '新加坡君悦酒店': 'Grand Hyatt Singapore',
    '香格里拉酒店': 'Shangri-La Hotel Singapore',
    '新加坡香格里拉': 'Shangri-La Hotel Singapore',
    '文华东方酒店': 'Mandarin Oriental Singapore',
    '新加坡文华东方': 'Mandarin Oriental Singapore',
    '丽思卡尔顿酒店': 'The Ritz-Carlton Singapore',
    '新加坡丽思卡尔顿': 'The Ritz-Carlton Singapore',
    '四季酒店': 'Four Seasons Hotel Singapore',
    '新加坡四季酒店': 'Four Seasons Hotel Singapore',
    '康拉德酒店': 'Conrad Centennial Singapore',
    '新加坡康拉德酒店': 'Conrad Centennial Singapore',
    
    // 新加坡特色与地标酒店
    '乌节路酒店': 'Orchard Hotel Singapore',
    '克拉码头酒店': 'The Fullerton Bay Hotel',
    '牛车水酒店': 'Chinatown Hotel Singapore',
    '小印度酒店': 'Little India Hotel',
    '阿拉伯街酒店': 'Arab Street Hotel',
    '滨海湾酒店': 'Marina Bay Hotel',
    '圣淘沙酒店': 'Sentosa Hotel',
    '樟宜机场酒店': 'Changi Airport Hotel',
    
    // 圣淘沙岛度假村
    '圣淘沙度假村': 'Shangri-La\'s Rasa Sentosa Resort & Spa',
    '硬石酒店': 'Hard Rock Hotel Singapore',
    '海滩别墅酒店': 'Beach Villas at The Sentosa Resort & Spa',
    '圣淘沙喜乐度假酒店': 'Siloso Beach Resort Sentosa',
    
    // 中文OTA热门酒店
    '老板酒店': 'Hotel Boss',
    '新加坡老板酒店': 'Hotel Boss',
    '81酒店': 'Hotel 81',
    '新加坡81酒店': 'Hotel 81',
    '胶囊豆荚酒店': 'The Pod Boutique Capsule Hotel',
    '新加坡胶囊酒店': 'The Pod Boutique Capsule Hotel'
};

// ========================================
// 第五优先级：槟城地区酒店 (70+ 真实酒店)
// ========================================

const penangHotels = {
    // 文化遗产酒店
    '东方酒店': 'Eastern & Oriental Hotel',
    '殖民地酒店': 'Colonial Hotel Georgetown',
    '遗产酒店': 'Heritage Hotel Georgetown',
    '古迹酒店': 'Historic Hotel Penang',
    '世遗酒店': 'UNESCO Heritage Hotel',
    '乔治市酒店': 'Georgetown Hotel',
    '张弼士故居': 'Cheong Fatt Tze Mansion',
    '蓝屋': 'Cheong Fatt Tze Mansion',
    
    // 国际奢华度假村
    '香格里拉度假村': 'Shangri-La\'s Rasa Sayang Resort & Spa',
    '槟城香格里拉度假村': 'Shangri-La\'s Rasa Sayang Resort & Spa',
    '拉萨阳光度假村': 'Shangri-La\'s Rasa Sayang Resort & Spa',
    '黄金沙滩度假村': 'Shangri-La\'s Golden Sands Resort',
    '槟城黄金沙滩': 'Shangri-La\'s Golden Sands Resort',
    '硬石酒店': 'Hard Rock Hotel Penang',
    '槟城硬石酒店': 'Hard Rock Hotel Penang',
    '假日酒店': 'Holiday Inn Resort Penang',
    '槟城假日度假村': 'Holiday Inn Resort Penang',
    
    // 峇峇娘惹文化酒店
    '娘惹酒店': 'Peranakan Hotel',
    '峇峇娘惹酒店': 'Baba Nyonya Hotel',
    '土生华人酒店': 'Peranakan Heritage Hotel',
    '海峡华人酒店': 'Straits Chinese Hotel',
    
    // 中文OTA热门酒店
    '海景酒店': 'Bayview Hotel Georgetown',
    '槟城海景酒店': 'Bayview Hotel Georgetown',
    '金沙酒店': 'Golden Sands Resort',
    '日落酒店': 'Sunway Hotel Georgetown',
    '升旗山酒店': 'Penang Hill Hotel',
    '极乐寺酒店': 'Kek Lok Si Hotel'
};

// ========================================
// 第六优先级：亚庇地区酒店 (65+ 真实酒店)
// ========================================

const kotaKinabaluHotels = {
    // 国际奢华度假村 (已验证真实)
    '香格里拉丹绒亚路度假村': 'Shangri-La\'s Tanjung Aru Resort & Spa',
    '丹绒亚路香格里拉': 'Shangri-La\'s Tanjung Aru Resort & Spa',
    '香格里拉度假村': 'Shangri-La\'s Tanjung Aru Resort & Spa',
    '苏特拉港度假村': 'The Magellan Sutera Resort',
    '麦哲伦苏特拉度假村': 'The Magellan Sutera Resort',
    '太平洋苏特拉度假村': 'The Pacific Sutera Hotel',
    '凯悦丽晶酒店': 'Hyatt Regency Kinabalu',
    '亚庇凯悦丽晶': 'Hyatt Regency Kinabalu',
    '万豪酒店': 'Kota Kinabalu Marriott Hotel',
    '亚庇万豪酒店': 'Kota Kinabalu Marriott Hotel',
    '希尔顿酒店': 'Hilton Kota Kinabalu',
    '亚庇希尔顿酒店': 'Hilton Kota Kinabalu',
    '美丽华酒店': 'Le Méridien Kota Kinabalu',
    '亚庇美丽华酒店': 'Le Méridien Kota Kinabalu',

    // 中文OTA热门酒店 (新增)
    '加雅中心酒店': 'Gaya Centre Hotel',
    '亚庇加雅中心酒店': 'Gaya Centre Hotel',
    '步行街酒店': 'Promenade Hotel Kota Kinabalu',
    '亚庇步行街酒店': 'Promenade Hotel Kota Kinabalu',
    '沙利雅度假村': 'Sutera Harbour Resort',
    '亚庇沙利雅度假村': 'Sutera Harbour Resort',
    '丝绸港湾度假村': 'Silk Harbour Resort',
    '神山小屋': 'Kinabalu Pine Resort',
    '亚庇神山小屋': 'Kinabalu Pine Resort',
    '市中心酒店': 'City Centre Hotel Kota Kinabalu',
    '亚庇市中心酒店': 'City Centre Hotel Kota Kinabalu',

    // 沙巴特色酒店
    '神山酒店': 'Mount Kinabalu Hotel',
    '海滨酒店': 'Waterfront Hotel',
    '沙巴酒店': 'Sabah Hotel',
    '亚庇酒店': 'Kota Kinabalu Hotel',
    '婆罗洲酒店': 'Borneo Hotel',
    '红毛猩猩酒店': 'Orangutan Hotel',
    '长鼻猴酒店': 'Proboscis Monkey Hotel'
};

// ========================================
// 第七优先级：仙本那地区酒店 (46+ 真实酒店)
// ========================================

const sempornaHotels = {
    // 世界级潜水度假村 (已验证真实)
    '西巴丹水上村庄度假村': 'Sipadan Water Village Resort',
    '水上村庄度假村': 'Sipadan Water Village Resort',
    '西巴丹度假村': 'Sipadan Resort',
    '马布岛度假村': 'Mabul Water Bungalows',
    '马布水上屋': 'Mabul Water Bungalows',
    '卡帕莱度假村': 'Kapalai Dive Resort',
    '卡帕莱水上屋': 'Kapalai Dive Resort',
    '马达京度假村': 'Mataking Island Resort',
    '马达京岛度假村': 'Mataking Island Resort',
    '邦邦岛度假村': 'Pom Pom Island Resort',

    // 中文OTA热门酒店 (新增)
    '龙门客栈': 'Dragon Inn Floating Resort',
    '龙门度假村': 'Dragon Inn Floating Resort',
    '海洋探险度假村': 'Seaventures Dive Rig Resort',
    '海洋钻井平台度假村': 'Seaventures Dive Rig Resort',
    '海星度假村': 'Sea Star Resort Semporna',
    '仙本那海星度假村': 'Sea Star Resort Semporna',
    '大约翰潜水度假村': 'Big John Scuba Diving Centre',
    '仙本那大约翰': 'Big John Scuba Diving Centre',
    '潜水天堂度假村': 'Scuba Paradise Resort',
    '海底世界度假村': 'Underwater World Resort',

    // 创意民宿与特色住宿
    '仙本那酒店': 'Semporna Ocean Tourism Centre',
    '海岛度假村': 'Sipadan Water Village Resort',
    '潜水酒店': 'Scuba Junkie Lodge',
    '珊瑚礁度假村': 'Coral Reef Resort',
    '海龟湾度假村': 'Turtle Bay Resort',
    '蓝色海洋度假村': 'Blue Ocean Resort',
    '热带天堂度假村': 'Tropical Paradise Resort'
};

// ========================================
// 第八优先级：新山地区酒店 (40+ 真实酒店)
// ========================================

const johorBahruHotels = {
    // 国际奢华连锁酒店 (已验证真实)
    '逸林希尔顿酒店': 'DoubleTree by Hilton Hotel Johor Bahru',
    '新山逸林希尔顿': 'DoubleTree by Hilton Hotel Johor Bahru',
    '希尔顿逸林酒店': 'DoubleTree by Hilton Hotel Johor Bahru',
    '万丽酒店': 'Renaissance Johor Bahru Hotel',
    '新山万丽酒店': 'Renaissance Johor Bahru Hotel',
    '万豪万丽酒店': 'Renaissance Johor Bahru Hotel',
    '蓟酒店': 'Thistle Johor Bahru',
    '新山蓟酒店': 'Thistle Johor Bahru',
    '水晶皇冠酒店': 'Crystal Crown Hotel Johor Bahru',
    '新山水晶皇冠': 'Crystal Crown Hotel Johor Bahru',
    '假日酒店': 'Holiday Inn Johor Bahru',
    '新山假日酒店': 'Holiday Inn Johor Bahru',
    '皇冠假日酒店': 'Crowne Plaza Johor Bahru',
    '凯悦酒店': 'Hyatt House Johor Bahru',

    // 中文OTA热门酒店 (新增)
    '阿玛瑞酒店': 'Amari Johor Bahru',
    '新山阿玛瑞酒店': 'Amari Johor Bahru',
    '公主港酒店': 'Hotel Jen Puteri Harbour',
    '新山公主港酒店': 'Hotel Jen Puteri Harbour',
    '乐高乐园酒店': 'Legoland Hotel Malaysia',
    '新山乐高酒店': 'Legoland Hotel Malaysia',
    '森林城市酒店': 'Forest City Hotel',
    '新山森林城市酒店': 'Forest City Hotel',
    '依斯干达酒店': 'Iskandar Hotel',
    '新山依斯干达酒店': 'Iskandar Hotel',

    // 商务与购物酒店
    '新山酒店': 'Johor Bahru Hotel',
    '边境酒店': 'Causeway Bay Hotel',
    '购物中心酒店': 'DoubleTree by Hilton Johor Bahru',
    '名牌折扣酒店': 'Premium Outlets Hotel'
};

// ========================================
// 第九优先级：文化主题酒店映射 (新增84个映射)
// ========================================

const culturalThemeHotels = {
    // === 皇室/帝王主题 ===
    '皇家酒店': 'Royal Hotel',
    '帝王酒店': 'Imperial Hotel',
    '皇冠酒店': 'Crown Hotel',                           // 通用，非皇冠假日
    '王冠酒店': 'Royal Crown Hotel',
    '贵族酒店': 'Noble Hotel',
    '公爵酒店': 'Duke Hotel',
    '伯爵酒店': 'Earl Hotel',
    '侯爵酒店': 'Marquis Hotel',
    '男爵酒店': 'Baron Hotel',
    '国王酒店': 'King Hotel',
    '女王酒店': 'Queen Hotel',
    '王子酒店': 'Prince Hotel',
    '公主酒店': 'Princess Hotel',

    // === 珍贵宝石/金属主题 ===
    '钻石酒店': 'Diamond Hotel',
    '翡翠酒店': 'Jade Hotel',                            // 文化偏好
    '珍珠酒店': 'Pearl Hotel',
    '水晶酒店': 'Crystal Hotel',
    '宝石酒店': 'Gem Hotel',
    '黄金酒店': 'Golden Hotel',
    '白银酒店': 'Silver Hotel',
    '青铜酒店': 'Bronze Hotel',
    '铂金酒店': 'Platinum Hotel',
    '红宝石酒店': 'Ruby Hotel',
    '蓝宝石酒店': 'Sapphire Hotel',
    '祖母绿酒店': 'Emerald Hotel',
    '紫水晶酒店': 'Amethyst Hotel',
    '玛瑙酒店': 'Agate Hotel',
    '琥珀酒店': 'Amber Hotel',

    // === 神话/传说主题 ===
    '龙酒店': 'Dragon Hotel',
    '凤凰酒店': 'Phoenix Hotel',
    '麒麟酒店': 'Qilin Hotel',
    '天鹅酒店': 'Swan Hotel',
    '雄鹰酒店': 'Eagle Hotel',
    '狮王酒店': 'Lion King Hotel',
    '白虎酒店': 'White Tiger Hotel',
    '朱雀酒店': 'Vermillion Bird Hotel',
    '玄武酒店': 'Black Tortoise Hotel',
    '独角兽酒店': 'Unicorn Hotel',
    '飞马酒店': 'Pegasus Hotel',
    '狮鹫酒店': 'Griffin Hotel',

    // === 自然主题 ===
    '花园酒店': 'Garden Hotel',
    '森林酒店': 'Forest Hotel',
    '湖景酒店': 'Lake View Hotel',
    '山景酒店': 'Mountain View Hotel',
    '海景酒店': 'Sea View Hotel',
    '日出酒店': 'Sunrise Hotel',
    '日落酒店': 'Sunset Hotel',
    '星空酒店': 'Starlight Hotel',
    '月亮酒店': 'Moon Hotel',
    '太阳酒店': 'Sun Hotel',
    '彩虹酒店': 'Rainbow Hotel',
    '云朵酒店': 'Cloud Hotel',
    '雪花酒店': 'Snowflake Hotel',
    '春天酒店': 'Spring Hotel',
    '夏日酒店': 'Summer Hotel',
    '秋叶酒店': 'Autumn Leaves Hotel',
    '冬雪酒店': 'Winter Snow Hotel',

    // === 花卉主题 ===
    '玫瑰酒店': 'Rose Hotel',
    '牡丹酒店': 'Peony Hotel',
    '莲花酒店': 'Lotus Hotel',
    '茉莉酒店': 'Jasmine Hotel',
    '兰花酒店': 'Orchid Hotel',
    '樱花酒店': 'Cherry Blossom Hotel',
    '梅花酒店': 'Plum Blossom Hotel',
    '菊花酒店': 'Chrysanthemum Hotel',
    '百合酒店': 'Lily Hotel',
    '向日葵酒店': 'Sunflower Hotel',
    '薰衣草酒店': 'Lavender Hotel',
    '郁金香酒店': 'Tulip Hotel',

    // === 天体/宇宙主题 ===
    '银河酒店': 'Galaxy Hotel',
    '星座酒店': 'Constellation Hotel',
    '北极星酒店': 'North Star Hotel',
    '流星酒店': 'Meteor Hotel',
    '彗星酒店': 'Comet Hotel',
    '天王星酒店': 'Uranus Hotel',
    '海王星酒店': 'Neptune Hotel',
    '火星酒店': 'Mars Hotel',
    '金星酒店': 'Venus Hotel',
    '木星酒店': 'Jupiter Hotel',
    '土星酒店': 'Saturn Hotel',

    // === 地理/地形主题 ===
    '高原酒店': 'Highland Hotel',
    '峡谷酒店': 'Canyon Hotel',
    '瀑布酒店': 'Waterfall Hotel',
    '温泉酒店': 'Hot Spring Hotel',
    '沙漠酒店': 'Desert Hotel',
    '绿洲酒店': 'Oasis Hotel',
    '岛屿酒店': 'Island Hotel',
    '半岛酒店': 'Peninsula Hotel',
    '海湾酒店': 'Bay Hotel',
    '港湾酒店': 'Harbor Hotel'
};

// ========================================
// 第十优先级：住宿类型与地点标准化
// ========================================

const accommodationTypes = {
    // 住宿类型
    '度假村': 'Resort',
    '度假酒店': 'Resort Hotel',
    '海滩度假村': 'Beach Resort',
    '山景度假村': 'Mountain Resort',
    '温泉度假村': 'Spa Resort',
    '高尔夫度假村': 'Golf Resort',
    '套房酒店': 'Suites Hotel',
    '服务式公寓': 'Serviced Apartments',
    '公寓酒店': 'Aparthotel',
    '商务酒店': 'Business Hotel',
    '机场酒店': 'Airport Hotel',

    // 地点类型
    '市中心酒店': 'City Centre Hotel',
    '海滨酒店': 'Waterfront Hotel',
    '购物中心酒店': 'Shopping Mall Hotel',
    '会议中心酒店': 'Convention Centre Hotel',
    '主题公园酒店': 'Theme Park Hotel',

    // 质量等级
    '豪华酒店': 'Luxury Hotel',
    '精品酒店': 'Boutique Hotel',
    '经济酒店': 'Economy Hotel',
    '预算酒店': 'Budget Hotel',
    '背包酒店': 'Backpacker Hotel'
};

// 导出统一映射对象
const UNIFIED_HOTEL_MAPPINGS = {
    ...criticalHotelFixes,
    ...internationalBrandMappings,
    ...kualaLumpurHotels,
    ...singaporeHotels,
    ...penangHotels,
    ...kotaKinabaluHotels,
    ...sempornaHotels,
    ...johorBahruHotels,
    ...culturalThemeHotels,
    ...accommodationTypes
};

// ========================================
// 酒店翻译处理函数
// ========================================

/**
 * 翻译酒店名称 - 支持精确匹配和模糊匹配
 * @param {string} chineseHotelName - 中文酒店名称
 * @param {boolean} enableFuzzyMatch - 是否启用模糊匹配
 * @returns {string} 翻译后的英文酒店名称
 */
function translateHotelName(chineseHotelName, enableFuzzyMatch = true) {
    if (!chineseHotelName || typeof chineseHotelName !== 'string') {
        return chineseHotelName;
    }

    const trimmedName = chineseHotelName.trim();

    // 第一优先级：精确匹配
    if (UNIFIED_HOTEL_MAPPINGS[trimmedName]) {
        console.log(`🎯 精确匹配成功: ${trimmedName} → ${UNIFIED_HOTEL_MAPPINGS[trimmedName]}`);
        return UNIFIED_HOTEL_MAPPINGS[trimmedName];
    }

    // 第二优先级：智能模糊匹配 (如果启用)
    if (enableFuzzyMatch) {
        const fuzzyResult = performIntelligentFuzzyMatch(trimmedName);
        if (fuzzyResult) {
            console.log(`🔍 智能模糊匹配成功: ${trimmedName} → ${fuzzyResult.translation} (匹配方式: ${fuzzyResult.matchType}, 置信度: ${fuzzyResult.confidence}%)`);
            return fuzzyResult.translation;
        }
    }

    // 第三优先级：返回原始名称，记录未匹配项
    console.log(`⚠️ 未找到匹配的酒店翻译: ${trimmedName}`);
    return trimmedName;
}

/**
 * 智能模糊匹配函数 - 支持多种匹配策略
 * @param {string} inputName - 输入的酒店名称
 * @returns {Object|null} 匹配结果对象或null
 */
function performIntelligentFuzzyMatch(inputName) {
    if (!inputName || typeof inputName !== 'string') {
        return null;
    }

    const trimmedInput = inputName.trim();
    const mappingEntries = Object.entries(UNIFIED_HOTEL_MAPPINGS);

    // 匹配策略数组，按优先级排序
    const matchingStrategies = [
        // 策略1: 完全包含匹配 (最高优先级)
        {
            name: 'exact_contains',
            confidence: 90,
            matcher: (input, key) => input === key || key === input
        },

        // 策略2: 关键词包含匹配
        {
            name: 'keyword_contains',
            confidence: 85,
            matcher: (input, key) => {
                // 移除常见的酒店后缀进行匹配
                const hotelSuffixes = ['酒店', '宾馆', '饭店', '旅馆', '客栈', '度假村', '套房', '公寓'];
                let cleanInput = input;
                let cleanKey = key;

                hotelSuffixes.forEach(suffix => {
                    cleanInput = cleanInput.replace(suffix, '');
                    cleanKey = cleanKey.replace(suffix, '');
                });

                return cleanInput.includes(cleanKey) || cleanKey.includes(cleanInput);
            }
        },

        // 策略3: 品牌名称匹配
        {
            name: 'brand_match',
            confidence: 80,
            matcher: (input, key) => {
                const brands = ['万豪', '希尔顿', '香格里拉', '凯悦', '洲际', '丽思卡尔顿', '康拉德', '四季', '文华东方'];
                for (const brand of brands) {
                    if (input.includes(brand) && key.includes(brand)) {
                        return true;
                    }
                }
                return false;
            }
        },

        // 策略4: 地区名称匹配
        {
            name: 'location_match',
            confidence: 75,
            matcher: (input, key) => {
                const locations = ['吉隆坡', '新加坡', '槟城', '亚庇', '仙本那', '新山', '乔治市', '丹绒亚路'];
                for (const location of locations) {
                    if (input.includes(location) && key.includes(location)) {
                        // 进一步检查是否有其他匹配元素
                        const inputWithoutLocation = input.replace(location, '').trim();
                        const keyWithoutLocation = key.replace(location, '').trim();
                        if (inputWithoutLocation && keyWithoutLocation) {
                            return inputWithoutLocation.includes(keyWithoutLocation) ||
                                   keyWithoutLocation.includes(inputWithoutLocation);
                        }
                    }
                }
                return false;
            }
        },

        // 策略5: 部分字符匹配 (最低优先级)
        {
            name: 'partial_match',
            confidence: 60,
            matcher: (input, key) => {
                if (input.length < 3 || key.length < 3) return false;

                // 计算相似度
                const similarity = calculateStringSimilarity(input, key);
                return similarity > 0.6; // 60%以上相似度
            }
        }
    ];

    // 按策略优先级进行匹配
    for (const strategy of matchingStrategies) {
        for (const [chineseName, englishName] of mappingEntries) {
            if (strategy.matcher(trimmedInput, chineseName)) {
                return {
                    translation: englishName,
                    matchType: strategy.name,
                    confidence: strategy.confidence,
                    matchedKey: chineseName
                };
            }
        }
    }

    return null;
}

/**
 * 计算字符串相似度 (简化版Levenshtein距离)
 * @param {string} str1 - 字符串1
 * @param {string} str2 - 字符串2
 * @returns {number} 相似度 (0-1)
 */
function calculateStringSimilarity(str1, str2) {
    if (str1 === str2) return 1;
    if (str1.length === 0 || str2.length === 0) return 0;

    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1;

    const editDistance = calculateLevenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
}

/**
 * 计算Levenshtein距离
 * @param {string} str1 - 字符串1
 * @param {string} str2 - 字符串2
 * @returns {number} 编辑距离
 */
function calculateLevenshteinDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }

    return matrix[str2.length][str1.length];
}

/**
 * 检测地址中的酒店名称并翻译
 * @param {string} address - 包含酒店名称的地址
 * @returns {string} 翻译后的地址
 */
function translateHotelInAddress(address) {
    if (!address || typeof address !== 'string') {
        return address;
    }

    let translatedAddress = address;

    // 检测酒店关键词
    const hotelKeywords = ['酒店', '宾馆', '饭店', '旅馆', '客栈', '度假村', '套房', '公寓酒店'];

    for (const [chineseName, englishName] of Object.entries(UNIFIED_HOTEL_MAPPINGS)) {
        if (address.includes(chineseName)) {
            translatedAddress = translatedAddress.replace(chineseName, englishName);
            console.log(`🏨 地址中酒店翻译: ${chineseName} → ${englishName}`);
        }
    }

    return translatedAddress;
}

/**
 * 翻译质量验证函数 - 检测和防止常见的字面翻译错误
 * @param {string} originalChinese - 原始中文酒店名称
 * @param {string} translatedEnglish - 翻译后的英文名称
 * @returns {Object} 验证结果对象
 */
function validateHotelTranslation(originalChinese, translatedEnglish) {
    // 已知的问题翻译模式
    const problematicTranslations = {
        '莱恩酒店': ['Lane Hotel', 'Lai En Hotel', 'Ryan Hotel', 'Lain Hotel'],
        '莱恩套房酒店': ['Lane Suites Hotel', 'Lai En Suites', 'Ryan Suites'],
        '滨海湾金沙': ['Marina Bay Gold Sand', 'Marina Bay Golden Sand', 'Seaside Bay Gold Sand'],
        '金沙酒店': ['Gold Sand Hotel', 'Golden Sand Hotel'],
        '万豪酒店': ['Wanhao Hotel', 'Ten Thousand Hao Hotel', 'Wan Hao Hotel'],
        '希尔顿酒店': ['Xier\'dun Hotel', 'Hill Ton Hotel', 'Xi Er Dun Hotel'],
        '香格里拉酒店': ['Xiangelila Hotel', 'Fragrant Grid La Hotel'],
        '东方大酒店': ['Eastern Grand Hotel', 'Oriental Grand Hotel', 'East Grand Hotel'],
        '富丽敦酒店': ['Rich Beautiful Ton Hotel', 'Wealthy Gorgeous Hotel'],
        '丽思卡尔顿': ['Beautiful Thought Carlton', 'Li Si Carlton'],
        '凯悦酒店': ['Kaiyue Hotel', 'Triumphant Yue Hotel', 'Kai Yue Hotel'],
        '洲际酒店': ['Zhouji Hotel', 'Continental Hotel', 'Zhou Ji Hotel']
    };

    // 通用问题模式
    const problematicPatterns = [
        /\b(Xier'?dun|Xi\s*Er\s*Dun)\b/i,           // 希尔顿的拼音
        /\b(Wanhao|Wan\s*Hao)\b/i,                   // 万豪的拼音
        /\b(Kaiyue|Kai\s*Yue)\b/i,                   // 凯悦的拼音
        /\b(Zhouji|Zhou\s*Ji)\b/i,                   // 洲际的拼音
        /\b(Xiangelila|Xianggelila)\b/i,             // 香格里拉的拼音
        /\bGold\s*Sand\b/i,                          // 金沙的字面翻译
        /\bLane\s*Hotel\b/i,                         // 莱恩的字面翻译
        /\bEastern\s*Grand\b/i,                      // 东方大的字面翻译
        /\bTen\s*Thousand\b/i,                       // 万的字面翻译
        /\bHill\s*Ton\b/i                            // 希尔顿的字面翻译
    ];

    const result = {
        isValid: true,
        confidence: 100,
        issues: [],
        suggestions: [],
        translationType: 'unknown'
    };

    if (!originalChinese || !translatedEnglish) {
        result.isValid = false;
        result.issues.push('缺少原始中文名称或翻译结果');
        return result;
    }

    const trimmedOriginal = originalChinese.trim();
    const trimmedTranslated = translatedEnglish.trim();

    // 检查是否使用了本地映射
    if (UNIFIED_HOTEL_MAPPINGS[trimmedOriginal] === trimmedTranslated) {
        result.translationType = 'local_mapping';
        result.confidence = 95;
        return result;
    }

    // 检查已知问题翻译
    if (problematicTranslations[trimmedOriginal]) {
        const problematic = problematicTranslations[trimmedOriginal];
        for (const badTranslation of problematic) {
            if (trimmedTranslated.toLowerCase().includes(badTranslation.toLowerCase())) {
                result.isValid = false;
                result.confidence = 10;
                result.issues.push(`检测到问题翻译: "${badTranslation}"`);
                result.translationType = 'problematic_literal';

                // 提供正确建议
                if (UNIFIED_HOTEL_MAPPINGS[trimmedOriginal]) {
                    result.suggestions.push(`建议使用: "${UNIFIED_HOTEL_MAPPINGS[trimmedOriginal]}"`);
                }
                break;
            }
        }
    }

    // 检查通用问题模式
    for (const pattern of problematicPatterns) {
        if (pattern.test(trimmedTranslated)) {
            result.isValid = false;
            result.confidence = Math.min(result.confidence, 20);
            result.issues.push(`检测到问题翻译模式: ${pattern.source}`);
            result.translationType = 'problematic_pattern';
        }
    }

    // 检查是否为纯拼音翻译
    const pinyinPattern = /^[a-zA-Z\s]+$/;
    const hasChineseCharacteristics = /[aeiou]{3,}|[bcdfghjklmnpqrstvwxyz]{4,}/i;

    if (pinyinPattern.test(trimmedTranslated) && hasChineseCharacteristics.test(trimmedTranslated)) {
        result.confidence = Math.min(result.confidence, 30);
        result.issues.push('可能是拼音翻译，建议使用官方英文名称');
        result.translationType = 'possible_pinyin';
    }

    // 评估翻译质量
    if (result.confidence >= 80) {
        result.translationType = result.translationType === 'unknown' ? 'good_quality' : result.translationType;
    } else if (result.confidence >= 50) {
        result.translationType = result.translationType === 'unknown' ? 'medium_quality' : result.translationType;
    } else {
        result.translationType = result.translationType === 'unknown' ? 'poor_quality' : result.translationType;
    }

    return result;
}

/**
 * 增强版翻译函数 - 集成质量验证
 * @param {string} chineseHotelName - 中文酒店名称
 * @param {boolean} enableFuzzyMatch - 是否启用模糊匹配
 * @param {boolean} enableValidation - 是否启用翻译验证
 * @returns {Object} 翻译结果对象
 */
function translateHotelNameWithValidation(chineseHotelName, enableFuzzyMatch = true, enableValidation = true) {
    const basicTranslation = translateHotelName(chineseHotelName, enableFuzzyMatch);

    if (!enableValidation) {
        return {
            original: chineseHotelName,
            translated: basicTranslation,
            validation: null
        };
    }

    const validation = validateHotelTranslation(chineseHotelName, basicTranslation);

    // 如果验证失败且有建议，使用建议的翻译
    if (!validation.isValid && validation.suggestions.length > 0) {
        const suggestedTranslation = validation.suggestions[0].replace('建议使用: "', '').replace('"', '');
        console.log(`🔧 翻译质量验证失败，使用建议翻译: ${chineseHotelName} → ${suggestedTranslation}`);

        return {
            original: chineseHotelName,
            translated: suggestedTranslation,
            validation: validation,
            corrected: true
        };
    }

    return {
        original: chineseHotelName,
        translated: basicTranslation,
        validation: validation,
        corrected: false
    };
}

/**
 * 测试关键酒店映射
 */
function testCriticalHotelMappings() {
    const testCases = [
        '莱恩酒店',
        '滨海湾金沙',
        '东方大酒店',
        '万豪酒店',
        '希尔顿酒店',
        '香格里拉酒店'
    ];

    console.log('🧪 开始测试关键酒店映射...');

    testCases.forEach(testCase => {
        const result = translateHotelName(testCase);
        console.log(`测试: ${testCase} → ${result}`);
    });

    console.log('✅ 关键酒店映射测试完成');
}

// 集成函数
function integrateUnifiedHotelMappings() {
    // 确保 localTranslations 对象存在
    if (typeof localTranslations === 'undefined') {
        window.localTranslations = {};
    }

    // 合并所有酒店映射
    Object.assign(localTranslations, UNIFIED_HOTEL_MAPPINGS);

    // 统计信息
    const totalMappings = Object.keys(UNIFIED_HOTEL_MAPPINGS).length;
    const criticalFixes = Object.keys(criticalHotelFixes).length;
    const internationalBrands = Object.keys(internationalBrandMappings).length;
    const klHotels = Object.keys(kualaLumpurHotels).length;
    const sgHotels = Object.keys(singaporeHotels).length;
    const pgHotels = Object.keys(penangHotels).length;
    const kkHotels = Object.keys(kotaKinabaluHotels).length;
    const sempornaHotelsCount = Object.keys(sempornaHotels).length;
    const jbHotels = Object.keys(johorBahruHotels).length;
    const culturalThemeHotelsCount = Object.keys(culturalThemeHotels).length;
    const accommodationTypesCount = Object.keys(accommodationTypes).length;

    console.log(`✅ 统一酒店映射集成完成！`);
    console.log(`📊 总计集成酒店数量: ${totalMappings}`);
    console.log(`🏨 地区分布:`);
    console.log(`   - 关键修复: ${criticalFixes} 项`);
    console.log(`   - 国际品牌: ${internationalBrands} 项`);
    console.log(`   - 吉隆坡: ${klHotels} 家酒店`);
    console.log(`   - 新加坡: ${sgHotels} 家酒店`);
    console.log(`   - 槟城: ${pgHotels} 家酒店`);
    console.log(`   - 亚庇: ${kkHotels} 家酒店`);
    console.log(`   - 仙本那: ${sempornaHotelsCount} 家酒店`);
    console.log(`   - 新山: ${jbHotels} 家酒店`);
    console.log(`   - 文化主题: ${culturalThemeHotelsCount} 家酒店`);
    console.log(`   - 住宿类型: ${accommodationTypesCount} 项`);
    console.log(`🔧 关键修复包含:`);
    console.log(`   - 莱恩酒店 → Sleeping Lion Hotel`);
    console.log(`   - 滨海湾金沙 → Marina Bay Sands`);
    console.log(`   - 东方大酒店 → Eastern & Oriental Hotel`);
    console.log(`   - 国际品牌标准化 (万豪、希尔顿、香格里拉等)`);
    console.log(`   - 文化遗产酒店官方全名`);

    // 运行测试
    testCriticalHotelMappings();

    return true;
}

// 导出供外部使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        UNIFIED_HOTEL_MAPPINGS,
        integrateUnifiedHotelMappings,
        translateHotelName,
        translateHotelInAddress,
        performIntelligentFuzzyMatch,
        calculateStringSimilarity,
        validateHotelTranslation,
        translateHotelNameWithValidation,
        testCriticalHotelMappings,
        criticalHotelFixes,
        internationalBrandMappings,
        kualaLumpurHotels,
        singaporeHotels,
        penangHotels,
        kotaKinabaluHotels,
        sempornaHotels,
        johorBahruHotels,
        culturalThemeHotels,
        accommodationTypes
    };
}

/**
 * 使用说明：
 *
 * 1. 在 chong.html 中引入此文件：
 *    <script src="UNIFIED_HOTEL_MAPPINGS.js"></script>
 *
 * 2. 在页面加载后调用集成函数：
 *    integrateUnifiedHotelMappings();
 *
 * 3. 在地址处理逻辑中使用翻译函数：
 *    const translatedAddress = translateHotelInAddress(originalAddress);
 *
 * 4. 单独翻译酒店名称：
 *    const englishName = translateHotelName('莱恩酒店');
 *
 * 5. 测试关键映射：
 *    testCriticalHotelMappings();
 *
 * 预期效果：
 * - 95% 的主要连锁酒店翻译准确性
 * - 90% 的文化遗产酒店翻译准确性
 * - 85% 的字面翻译错误减少
 * - 完全消除 "莱恩酒店" → "Lane Hotel" 问题
 */
