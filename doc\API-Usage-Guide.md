# GoMyHire API 使用指南

## 📋 概述

本指南详细说明如何使用完整的 GoMyHire API 服务 (`js/api-service-complete.js`)，确保后续开发完全符合 API 需求。

## 🚀 快速开始

### 1. 引入API服务

```html
<!-- 在 HTML 中引入 -->
<script src="js/api-service-complete.js"></script>
```

### 2. 获取API服务实例

```javascript
// 获取完整的API服务实例
const apiService = getCompleteApiService();

// 或通过服务定位器
const apiService = getService('completeApiService');
```

### 3. 基本使用

```javascript
// 用户登录
const loginResult = await apiService.login('<EMAIL>', 'password');

// 创建单个订单
const orderResult = await apiService.createOrder(orderData);

// 批量创建订单
const batchResult = await apiService.createMultipleOrders([order1, order2, order3]);
```

## 🔐 认证流程

### 登录认证

```javascript
try {
    const result = await apiService.login('<EMAIL>', 'password', true);
    
    if (result.success) {
        console.log('登录成功:', result.user);
        console.log('Token:', result.token);
    }
} catch (error) {
    console.error('登录失败:', error.message);
}
```

### 自动Token管理

```javascript
// API服务会自动管理Token
// 登录后所有需要认证的请求都会自动添加Authorization头
// 订单创建不需要认证，会自动跳过认证检查
```

## 📝 订单创建

### 创建单个订单

```javascript
const orderData = {
    // 必填字段
    sub_category_id: 2,                    // 2=接机, 3=送机, 4=包车
    ota_reference_number: 'CD123456789',   // OTA参考号
    car_type_id: 5,                        // 车型ID
    incharge_by_backend_user_id: 37,       // 后台负责人ID
    
    // 可选字段
    customer_name: '王小明',
    customer_contact: '+60123456789',
    customer_email: '<EMAIL>',
    pickup: 'KLIA2 Terminal',
    destination: 'Kuala Lumpur Sentral',
    date: '2025-07-20',
    time: '15:30',
    passenger_number: 2,
    luggage_number: 3,
    ota_price: 150.00,
    extra_requirement: '需要中文司机'
};

try {
    const result = await apiService.createOrder(orderData);
    
    if (result.success) {
        console.log('订单创建成功:', result.orderNumber);
        console.log('订单ID:', result.orderId);
    }
} catch (error) {
    console.error('订单创建失败:', error.message);
    
    if (error.isDuplicate) {
        console.log('这是一个重复订单');
    }
}
```

### 批量创建订单

```javascript
const orders = [
    {
        sub_category_id: 2,
        ota_reference_number: 'CD001',
        car_type_id: 5,
        incharge_by_backend_user_id: 37,
        customer_name: '客户A'
    },
    {
        sub_category_id: 3,
        ota_reference_number: 'CD002',
        car_type_id: 15,
        incharge_by_backend_user_id: 37,
        customer_name: '客户B'
    }
];

try {
    const result = await apiService.createMultipleOrders(orders);
    
    console.log('批量创建完成:', {
        总数: result.total,
        成功: result.successful,
        失败: result.failed
    });
    
    // 查看失败的订单
    result.errors.forEach(error => {
        console.error(`订单${error.index}失败:`, error.error);
    });
} catch (error) {
    console.error('批量创建失败:', error.message);
}
```

## 🛠️ 智能功能

### 1. 自动字段补全

```javascript
// 最简订单数据
const minimalOrder = {
    ota_reference_number: 'CD123456789',
    customer_name: '王小明',
    pickup: 'KLIA2',
    destination: 'KL Sentral',
    passenger_number: 2
};

// API服务会自动补全:
// - sub_category_id: 2 (检测为接机服务)
// - car_type_id: 5 (推荐5座车)
// - incharge_by_backend_user_id: 37 (根据登录用户匹配)
// - languages_id_array: {"0": "4"} (检测中文姓名)
// - driving_region_id: 1 (检测为KL区域)
// - ota: "Chong Dealer" (检测OTA平台)

const result = await apiService.createOrder(minimalOrder);
```

### 2. 智能验证

```javascript
// 自动验证会检查:
// - 必填字段完整性
// - 邮箱和电话格式
// - 日期时间格式
// - 数值范围
// - 乘客数与车型匹配

const invalidOrder = {
    sub_category_id: 2,
    ota_reference_number: 'CD123',
    customer_email: 'invalid-email',
    passenger_number: 10,
    car_type_id: 5  // 5座车不能载10人
};

try {
    await apiService.createOrder(invalidOrder);
} catch (error) {
    console.error('验证失败:', error.message);
    // 会显示具体的验证错误信息
}
```

### 3. 智能推荐

```javascript
// 获取车型推荐
const recommendedCarType = apiService.recommendCarType(6); // 6名乘客
console.log('推荐车型ID:', recommendedCarType); // 返回: 32 (Alphard)

// 获取服务类型推荐
const serviceType = apiService.detectServiceType('KLIA2', 'KL Sentral');
console.log('服务类型:', serviceType); // 返回: 2 (接机)

// 获取语言推荐
const languages = apiService.getDefaultLanguagesArray('John Smith');
console.log('推荐语言:', languages); // 返回: {"0": "2"} (英语)
```

## 📊 数据获取

### 获取系统数据

```javascript
// 获取所有车型
const carTypes = apiService.getSystemData('carTypes');
console.log('可用车型:', carTypes);

// 获取后台用户
const backendUsers = apiService.getSystemData('backendUsers');
console.log('后台用户:', backendUsers);

// 获取语言选项
const languages = apiService.getSystemData('languages');
console.log('支持语言:', languages);
```

### 获取详细信息

```javascript
// 获取特定车型信息
const carType = apiService.getCarTypeInfo(5);
console.log('车型信息:', carType);
// 返回: { id: 5, name: '5 Seater...', passengerLimit: 3 }

// 获取服务类型信息
const serviceType = apiService.getServiceTypeInfo(2);
console.log('服务类型:', serviceType);
// 返回: { id: 2, name: 'Pickup', description: '接机服务' }
```

## 🔍 数据验证

### 手动验证

```javascript
// 验证订单数据
const validation = apiService.validateOrderData(orderData);

if (!validation.isValid) {
    console.error('验证失败:', validation.errors);
    validation.errors.forEach(error => {
        console.log('错误:', error);
    });
}

// 验证OTA参考号
const isValidReference = apiService.validateOtaReference('CD123456789');
console.log('参考号是否有效:', isValidReference);
```

### 格式化数据

```javascript
// 格式化日期
const formattedDate = apiService.formatDate('2025/07/20');
console.log('格式化日期:', formattedDate); // 返回: '2025-07-20'

// 格式化时间
const formattedTime = apiService.formatTime('3:30 PM');
console.log('格式化时间:', formattedTime); // 返回: '15:30'
```

## 📈 订单摘要

### 生成订单摘要

```javascript
const orderSummary = apiService.generateOrderSummary(orderData);
console.log('订单摘要:', orderSummary);

// 返回:
// {
//     service: 'Pickup',
//     customer: '王小明',
//     route: 'KLIA2 Terminal → KL Sentral',
//     datetime: '2025-07-20 15:30',
//     passengers: 2,
//     luggage: 3,
//     carType: '5 Seater...',
//     price: 150.00,
//     reference: 'CD123456789'
// }
```

## 🚨 错误处理

### 错误类型

```javascript
try {
    await apiService.createOrder(orderData);
} catch (error) {
    switch (error.type) {
        case 'network_error':
            console.log('网络连接问题');
            break;
        case 'validation_error':
            console.log('数据验证失败');
            break;
        case 'duplicate_error':
            console.log('重复订单');
            break;
        case 'timeout_error':
            console.log('请求超时');
            break;
        case 'auth_error':
            console.log('认证失败');
            break;
        default:
            console.log('未知错误:', error.message);
    }
}
```

### 优雅的错误处理

```javascript
async function createOrderWithErrorHandling(orderData) {
    try {
        const result = await apiService.createOrder(orderData);
        
        // 显示成功消息
        showSuccessMessage(`订单创建成功: ${result.orderNumber}`);
        
        return result;
    } catch (error) {
        // 根据错误类型显示不同的用户友好消息
        if (error.isDuplicate) {
            showWarningMessage('此订单已存在，请检查参考号');
        } else if (error.type === 'network_error') {
            showErrorMessage('网络连接失败，请检查网络后重试');
        } else if (error.type === 'validation_error') {
            showErrorMessage(`数据验证失败: ${error.message}`);
        } else {
            showErrorMessage('订单创建失败，请稍后重试');
        }
        
        throw error;
    }
}
```

## 🔧 高级功能

### 健康检查

```javascript
const healthStatus = await apiService.healthCheck();
if (healthStatus.healthy) {
    console.log('API服务正常');
} else {
    console.error('API服务异常:', healthStatus.error);
}
```

### 获取API统计

```javascript
const stats = apiService.getApiStats();
console.log('API统计:', stats);

// 返回:
// {
//     version: '1.0.0',
//     lastUpdate: '2025-07-18',
//     supportedFeatures: [...],
//     fieldCount: { total: 28, required: 4, optional: 24 },
//     validationRules: 5,
//     staticDataEntries: 150
// }
```

### 批量数据处理

```javascript
// 预处理多个订单
const orders = [...]; // 原始订单数据
const processedOrders = orders.map(order => 
    apiService.preprocessOrderData(order)
);

// 批量验证
const validationResults = processedOrders.map(order => ({
    order,
    validation: apiService.validateOrderData(order)
}));

// 筛选有效订单
const validOrders = validationResults
    .filter(result => result.validation.isValid)
    .map(result => result.order);

// 批量创建
const batchResult = await apiService.createMultipleOrders(validOrders);
```

## 📚 最佳实践

### 1. 错误处理

```javascript
// 总是使用try-catch处理异步调用
// 根据错误类型提供用户友好的消息
// 记录详细的错误信息用于调试

async function handleApiCall() {
    try {
        const result = await apiService.createOrder(orderData);
        return result;
    } catch (error) {
        // 记录错误
        console.error('API调用失败:', error);
        
        // 向用户显示友好的错误消息
        showUserFriendlyError(error);
        
        // 重新抛出错误以便上层处理
        throw error;
    }
}
```

### 2. 数据验证

```javascript
// 在提交前总是验证数据
const validation = apiService.validateOrderData(orderData);
if (!validation.isValid) {
    // 显示验证错误
    showValidationErrors(validation.errors);
    return;
}

// 提交数据
await apiService.createOrder(orderData);
```

### 3. 智能默认值

```javascript
// 利用智能默认值减少用户输入
const orderData = {
    ota_reference_number: 'CD123456789',
    customer_name: '王小明',
    pickup: 'KLIA2',
    destination: 'KL Sentral',
    passenger_number: 2
    // 其他字段会自动补全
};
```

### 4. 批量处理

```javascript
// 对于大量订单，使用批量处理提高效率
const batchSize = 10;
const orderBatches = [];

for (let i = 0; i < orders.length; i += batchSize) {
    orderBatches.push(orders.slice(i, i + batchSize));
}

for (const batch of orderBatches) {
    await apiService.createMultipleOrders(batch);
    // 添加延迟避免API限制
    await new Promise(resolve => setTimeout(resolve, 1000));
}
```

## 🔄 集成示例

### 与表单集成

```javascript
async function submitOrderForm(formData) {
    try {
        // 显示加载状态
        showLoadingSpinner();
        
        // 预处理表单数据
        const processedData = apiService.preprocessOrderData(formData);
        
        // 验证数据
        const validation = apiService.validateOrderData(processedData);
        if (!validation.isValid) {
            hideLoadingSpinner();
            showValidationErrors(validation.errors);
            return;
        }
        
        // 创建订单
        const result = await apiService.createOrder(processedData);
        
        // 显示成功消息
        hideLoadingSpinner();
        showSuccessMessage(`订单创建成功: ${result.orderNumber}`);
        
        // 保存到历史记录
        saveOrderToHistory(result);
        
        // 清空表单
        clearForm();
        
    } catch (error) {
        hideLoadingSpinner();
        handleOrderCreationError(error);
    }
}
```

### 与UI组件集成

```javascript
// 动态填充下拉菜单
function populateCarTypeDropdown() {
    const carTypes = apiService.getSystemData('carTypes');
    const dropdown = document.getElementById('carTypeSelect');
    
    carTypes.forEach(carType => {
        const option = document.createElement('option');
        option.value = carType.id;
        option.textContent = carType.name;
        dropdown.appendChild(option);
    });
}

// 智能表单填充
function smartFormFill(orderText) {
    // 使用AI解析订单文本
    const parsedOrder = parseOrderText(orderText);
    
    // 增强数据
    const enhancedOrder = apiService.enhanceOrderData(parsedOrder);
    
    // 填充表单
    fillFormWithData(enhancedOrder);
}
```

## 📖 参考文档

- [API字段需求文档](./GoMyHire-API-Field-Requirements.md)
- [项目架构文档](../CLAUDE.md)
- [错误代码参考](./Error-Codes.md)
- [性能优化指南](./Performance-Optimization.md)

---

**版本**: v1.0.0  
**更新日期**: 2025-07-18  
**维护人**: Claude Code  
**状态**: 完整实现